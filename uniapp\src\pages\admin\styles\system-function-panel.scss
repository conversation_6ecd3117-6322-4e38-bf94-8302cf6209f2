// 系统功能管理面板样式
.system-function-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;

  // 功能导航栏
  .function-nav {
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
    padding: 16px 24px;

    .nav-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
    }

    .nav-tabs {
      display: flex;
      gap: 8px;
      overflow-x: auto;

      .nav-tab {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px 16px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 80px;
        position: relative;

        &:hover {
          background-color: #f0f8ff;
        }

        &.active {
          background-color: #1890ff;
          color: #fff;

          .tab-icon,
          .tab-label {
            color: #fff;
          }
        }

        .tab-icon {
          font-size: 20px;
          margin-bottom: 4px;
        }

        .tab-label {
          font-size: 12px;
          color: #666;
          text-align: center;
        }

        .tab-badge {
          position: absolute;
          top: 4px;
          right: 4px;
          background-color: #ff4d4f;
          color: #fff;
          font-size: 10px;
          padding: 2px 6px;
          border-radius: 10px;
          min-width: 16px;
          text-align: center;
        }
      }
    }
  }

  // 功能内容区域
  .function-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;

    .content-section {
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      height: 100%;
    }
  }

  // 快速操作面板
  .quick-actions {
    background-color: #fff;
    border-top: 1px solid #e8e8e8;
    padding: 16px 24px;

    .actions-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
    }

    .actions-grid {
      display: flex;
      gap: 12px;
      overflow-x: auto;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 80px;

        &:hover {
          border-color: #1890ff;
          background-color: #f0f8ff;
        }

        .action-icon {
          font-size: 18px;
          margin-bottom: 4px;
        }

        .action-label {
          font-size: 12px;
          color: #666;
          text-align: center;
        }
      }
    }
  }
}

}

// 移动端适配
@media (max-width: 768px) {
  .system-function-panel {
    .function-nav {
      padding: 12px 16px;

      .nav-title {
        font-size: 16px;
        margin-bottom: 12px;
      }

      .nav-tabs {
        gap: 6px;

        .nav-tab {
          padding: 8px 12px;
          min-width: 60px;

          .tab-icon {
            font-size: 16px;
          }

          .tab-label {
            font-size: 11px;
          }
        }
      }
    }

    .function-content {
      padding: 16px;

      .content-section {
        padding: 16px;
      }
    }

    .quick-actions {
      padding: 12px 16px;

      .actions-grid {
        gap: 8px;

        .action-item {
          padding: 8px;
          min-width: 60px;

          .action-icon {
            font-size: 16px;
          }

          .action-label {
            font-size: 11px;
          }
        }
      }
    }
  }
}