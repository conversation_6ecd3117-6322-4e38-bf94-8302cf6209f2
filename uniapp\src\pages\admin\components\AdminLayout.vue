<template>
  <view class="admin-layout" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
    <!-- 左侧导航栏 -->
    <view class="sidebar-container" :class="{ collapsed: sidebarCollapsed }">
      <AdminSidebar />
    </view>
    
    <!-- 主内容区域 -->
    <view class="main-container">
      <!-- 顶部用户栏 -->
      <view class="header-container">
        <AdminHeader @toggle-sidebar="toggleSidebar" />
      </view>
      
      <!-- 主内容区 -->
      <view class="content-container">
        <slot name="content">
          <AdminContent />
        </slot>
      </view>
    </view>
    
    <!-- 移动端遮罩层 -->
    <view 
      v-if="showMobileMask" 
      class="mobile-mask" 
      @click="closeMobileSidebar"
    ></view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import AdminSidebar from './AdminSidebar.vue'
import AdminHeader from './AdminHeader.vue'
import AdminContent from './AdminContent.vue'

// 响应式状态
const sidebarCollapsed = ref(false)
const isMobile = ref(false)

// 计算属性
const showMobileMask = computed(() => isMobile.value && !sidebarCollapsed.value)

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const closeMobileSidebar = () => {
  if (isMobile.value) {
    sidebarCollapsed.value = true
  }
}

const checkScreenSize = () => {
  const systemInfo = uni.getSystemInfoSync()
  isMobile.value = systemInfo.windowWidth <= 768
  
  // 移动端默认折叠侧边栏
  if (isMobile.value && !sidebarCollapsed.value) {
    sidebarCollapsed.value = true
  }
}

// 生命周期
onMounted(() => {
  checkScreenSize()
  
  // 监听窗口大小变化
  uni.onWindowResize(() => {
    checkScreenSize()
  })
})

onUnmounted(() => {
  uni.offWindowResize()
})

// 暴露方法给父组件
defineExpose({
  toggleSidebar,
  sidebarCollapsed: computed(() => sidebarCollapsed.value)
})
</script>

<style lang="scss" scoped>
.admin-layout {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;

  .sidebar-container {
    width: 240px;
    background-color: #001529;
    transition: all 0.3s ease;
    flex-shrink: 0;
    z-index: 1000;

    &.collapsed {
      width: 64px;
    }
  }

  .main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-width: 0; // 防止flex子项溢出

    .header-container {
      height: 64px;
      background-color: #fff;
      border-bottom: 1px solid #e8e8e8;
      flex-shrink: 0;
      z-index: 999;
    }

    .content-container {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background-color: #f5f5f5;
    }
  }

  .mobile-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.45);
    z-index: 999;
  }

  // 侧边栏折叠状态
  &.sidebar-collapsed {
    .sidebar-container {
      width: 64px;
    }
  }
}

// 响应式布局
@media (max-width: 768px) {
  .admin-layout {
    .sidebar-container {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      z-index: 1001;
      transform: translateX(-100%);
      transition: transform 0.3s ease;

      &:not(.collapsed) {
        transform: translateX(0);
      }
    }

    .main-container {
      width: 100%;
      margin-left: 0;
    }

    &.sidebar-collapsed {
      .sidebar-container {
        transform: translateX(-100%);
      }
    }
  }
}

// 平板布局
@media (min-width: 769px) and (max-width: 1024px) {
  .admin-layout {
    .sidebar-container {
      width: 200px;

      &.collapsed {
        width: 64px;
      }
    }
  }
}

// 大屏幕优化
@media (min-width: 1200px) {
  .admin-layout {
    .sidebar-container {
      width: 260px;

      &.collapsed {
        width: 80px;
      }
    }

    .main-container {
      .content-container {
        padding: 24px;
      }
    }
  }
}

// 暗色主题支持
.admin-layout.dark-theme {
  background-color: #141414;

  .main-container {
    .header-container {
      background-color: #1f1f1f;
      border-bottom-color: #303030;
    }

    .content-container {
      background-color: #141414;
    }
  }
}

// 动画效果
.admin-layout {
  * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// 滚动条样式
.content-container {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
</style>