import { ref, reactive, computed } from 'vue'
import type { CrudOperation, QueryParams, PaginationConfig } from '../types/admin'
import type { PageResult } from '@/types/global'
import { globalUIManager } from './useUIManager'

/**
 * 通用CRUD操作composable
 * @param crudApi CRUD操作API接口
 * @param options 配置选项
 */
export function useCrud<T extends Record<string, any>>(
  crudApi: CrudOperation<T>,
  options: {
    /** 初始分页配置 */
    initialPagination?: Partial<PaginationConfig>
    /** 是否自动加载数据 */
    autoLoad?: boolean
    /** 缓存键 */
    cacheKey?: string
  } = {}
) {
  // 响应式状态
  const loading = ref(false)
  const submitting = ref(false)
  const data = ref<T[]>([])
  const total = ref(0)
  const selectedItems = ref<T[]>([])
  
  // 分页配置
  const pagination = reactive<PaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    ...options.initialPagination
  })
  
  // 查询参数
  const queryParams = reactive<QueryParams>({
    page: pagination.current,
    pageSize: pagination.pageSize,
    keyword: '',
    sortBy: '',
    sortOrder: 'asc'
  })
  
  // 错误状态
  const error = ref<string>('')
  
  // 计算属性
  const hasData = computed(() => data.value.length > 0)
  const isEmpty = computed(() => !loading.value && !hasData.value)
  const hasSelection = computed(() => selectedItems.value.length > 0)
  
  /**
   * 显示成功消息
   */
  const showSuccess = (message: string) => {
    globalUIManager.showSuccess(message)
  }

  /**
   * 显示错误消息
   */
  const showError = (message: string) => {
    globalUIManager.showError(message)
    error.value = message
  }
  
  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = ''
  }
  
  /**
   * 加载数据列表
   */
  const loadData = async (params?: Partial<QueryParams>) => {
    let loadingToastId: number | undefined

    try {
      loading.value = true
      clearError()

      // 显示加载提示
      if (options.showLoadingToast !== false) {
        loadingToastId = globalUIManager.showLoading('加载数据中...')
      }

      const mergedParams = {
        ...queryParams,
        ...params,
        page: pagination.current,
        pageSize: pagination.pageSize
      }

      const result: PageResult<T> = await crudApi.list(mergedParams)
      
      data.value = result.items || []
      total.value = result.total || 0
      pagination.total = result.total || 0
      
      // 缓存数据（如果需要）
      if (options.cacheKey) {
        uni.setStorageSync(`crud_cache_${options.cacheKey}`, {
          data: data.value,
          total: total.value,
          timestamp: Date.now()
        })
      }
      
    } catch (err: any) {
      console.error('Load data error:', err)
      showError(err.message || '加载数据失败')
      data.value = []
      total.value = 0
    } finally {
      loading.value = false

      // 隐藏加载提示
      if (loadingToastId) {
        globalUIManager.hideLoading(loadingToastId)
      }
    }
  }
  
  /**
   * 刷新数据
   */
  const refresh = () => {
    return loadData()
  }
  
  /**
   * 搜索数据
   */
  const search = (keyword: string) => {
    queryParams.keyword = keyword
    pagination.current = 1
    return loadData()
  }
  
  /**
   * 排序数据
   */
  const sort = (field: string, order: 'asc' | 'desc') => {
    queryParams.sortBy = field
    queryParams.sortOrder = order
    pagination.current = 1
    return loadData()
  }
  
  /**
   * 分页变更
   */
  const changePage = (page: number) => {
    pagination.current = page
    return loadData()
  }
  
  /**
   * 每页大小变更
   */
  const changePageSize = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    queryParams.pageSize = pageSize
    return loadData()
  }
  
  /**
   * 创建记录
   */
  const create = async (formData: Partial<T>): Promise<T | null> => {
    try {
      submitting.value = true
      clearError()
      
      const result = await crudApi.create(formData)
      
      showSuccess('创建成功')
      
      // 刷新数据
      await refresh()
      
      return result
    } catch (err: any) {
      console.error('Create error:', err)
      showError(err.message || '创建失败')
      return null
    } finally {
      submitting.value = false
    }
  }
  
  /**
   * 更新记录
   */
  const update = async (id: number, formData: Partial<T>): Promise<T | null> => {
    try {
      submitting.value = true
      clearError()
      
      const result = await crudApi.update(id, formData)
      
      showSuccess('更新成功')
      
      // 更新本地数据
      const index = data.value.findIndex(item => item.id === id)
      if (index !== -1) {
        data.value[index] = { ...data.value[index], ...result }
      }
      
      return result
    } catch (err: any) {
      console.error('Update error:', err)
      showError(err.message || '更新失败')
      return null
    } finally {
      submitting.value = false
    }
  }
  
  /**
   * 删除记录
   */
  const remove = async (id: number, itemName?: string): Promise<boolean> => {
    try {
      // 显示确认对话框
      const confirmed = await globalUIManager.showDeleteConfirm(itemName)

      if (!confirmed) {
        return false
      }
      
      submitting.value = true
      clearError()
      
      const success = await crudApi.delete(id)
      
      if (success) {
        showSuccess('删除成功')
        
        // 从本地数据中移除
        const index = data.value.findIndex(item => item.id === id)
        if (index !== -1) {
          data.value.splice(index, 1)
          total.value -= 1
          pagination.total -= 1
        }
        
        // 如果当前页没有数据了，回到上一页
        if (data.value.length === 0 && pagination.current > 1) {
          pagination.current -= 1
          await loadData()
        }
      }
      
      return success
    } catch (err: any) {
      console.error('Delete error:', err)
      showError(err.message || '删除失败')
      return false
    } finally {
      submitting.value = false
    }
  }
  
  /**
   * 批量删除
   */
  const batchRemove = async (ids: number[]): Promise<boolean> => {
    if (ids.length === 0) {
      showError('请选择要删除的记录')
      return false
    }
    
    try {
      // 显示确认对话框
      const confirmed = await globalUIManager.showBatchConfirm('删除', ids.length, '记录')

      if (!confirmed) {
        return false
      }
      
      submitting.value = true
      clearError()
      
      // 批量删除
      const promises = ids.map(id => crudApi.delete(id))
      const results = await Promise.allSettled(promises)
      
      const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length
      const failCount = ids.length - successCount
      
      if (successCount > 0) {
        showSuccess(`成功删除 ${successCount} 条记录${failCount > 0 ? `，${failCount} 条失败` : ''}`)
        
        // 清空选择
        selectedItems.value = []
        
        // 刷新数据
        await refresh()
      } else {
        showError('批量删除失败')
      }
      
      return successCount > 0
    } catch (err: any) {
      console.error('Batch delete error:', err)
      showError(err.message || '批量删除失败')
      return false
    } finally {
      submitting.value = false
    }
  }
  
  /**
   * 获取单条记录
   */
  const get = async (id: number): Promise<T | null> => {
    try {
      loading.value = true
      clearError()
      
      const result = await crudApi.get(id)
      return result
    } catch (err: any) {
      console.error('Get record error:', err)
      showError(err.message || '获取记录失败')
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 自动加载数据
  if (options.autoLoad !== false) {
    loadData()
  }
  
  return {
    // 状态
    loading,
    submitting,
    data,
    total,
    selectedItems,
    pagination,
    queryParams,
    error,
    
    // 计算属性
    hasData,
    isEmpty,
    hasSelection,
    
    // 方法
    loadData,
    refresh,
    search,
    sort,
    changePage,
    changePageSize,
    create,
    update,
    remove,
    batchRemove,
    get,
    clearError,
    showSuccess,
    showError
  }
}
