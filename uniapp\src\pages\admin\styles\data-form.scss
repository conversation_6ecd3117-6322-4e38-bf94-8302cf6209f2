// 通用数据表单样式
.data-form {
  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .form-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }

    .form-actions {
      display: flex;
      gap: 8px;

      .action-btn {
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        color: #333;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;

          &:hover {
            background-color: #40a9ff;
          }
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;

          &:hover {
            border-color: #d9d9d9;
            color: #333;
            background-color: #fff;
          }
        }
      }
    }
  }

  .form-body {
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;

      &.single-column {
        grid-template-columns: 1fr;
      }

      &.two-columns {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    .form-field {
      .field-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;

        .required-mark {
          color: #ff4d4f;
          margin-left: 4px;
        }
      }

      .field-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.3s ease;

        &:focus {
          outline: none;
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &.error {
          border-color: #ff4d4f;

          &:focus {
            box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
          }
        }

        &:disabled {
          background-color: #f5f5f5;
          color: #999;
          cursor: not-allowed;
        }

        &.textarea {
          min-height: 80px;
          resize: vertical;
        }
      }

      .field-select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 14px;
        background-color: #fff;
        cursor: pointer;
        transition: border-color 0.3s ease;

        &:focus {
          outline: none;
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &.error {
          border-color: #ff4d4f;
        }

        &:disabled {
          background-color: #f5f5f5;
          color: #999;
          cursor: not-allowed;
        }
      }

      .field-radio-group,
      .field-checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .radio-item,
        .checkbox-item {
          display: flex;
          align-items: center;
          cursor: pointer;

          .radio-input,
          .checkbox-input {
            margin-right: 8px;
          }

          .radio-label,
          .checkbox-label {
            font-size: 14px;
            color: #333;
            user-select: none;
          }
        }
      }

      .field-error {
        font-size: 12px;
        color: #ff4d4f;
        margin-top: 4px;
        display: flex;
        align-items: center;

        .error-icon {
          margin-right: 4px;
          font-size: 12px;
        }
      }

      .field-help {
        font-size: 12px;
        color: #999;
        margin-top: 4px;
      }

      // 开关切换样式
      .field-switch {
        .switch-container {
          display: flex;
          align-items: center;
          cursor: pointer;
          user-select: none;

          .switch-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;

            &:checked + .switch-slider {
              background-color: #1890ff;

              &:before {
                transform: translateX(20px);
              }
            }

            &:disabled + .switch-slider {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }

          .switch-slider {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
            background-color: #ccc;
            border-radius: 24px;
            transition: 0.3s;

            &:before {
              content: '';
              position: absolute;
              height: 20px;
              width: 20px;
              left: 2px;
              top: 2px;
              background-color: white;
              border-radius: 50%;
              transition: 0.3s;
            }
          }

          .switch-text {
            margin-left: 8px;
            font-size: 14px;
            color: #333;
          }
        }
      }

      // 文件上传样式
      .field-file {
        .file-input {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          font-size: 14px;
          cursor: pointer;
          transition: border-color 0.3s ease;

          &:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }

          &:disabled {
            background-color: #f5f5f5;
            cursor: not-allowed;
          }
        }

        .file-preview {
          margin-top: 8px;

          .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 4px 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
            margin-bottom: 4px;
            font-size: 12px;
            color: #666;

            .file-remove {
              background: none;
              border: none;
              color: #ff4d4f;
              cursor: pointer;
              font-size: 16px;
              font-weight: bold;
              padding: 0;
              margin-left: 8px;

              &:hover {
                color: #ff7875;
              }
            }
          }
        }
      }

      // 颜色选择器样式
      .color-input {
        width: 60px;
        height: 40px;
        padding: 4px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        cursor: pointer;

        &:focus {
          outline: none;
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &:disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }

      // 范围滑块样式
      .field-range {
        display: flex;
        align-items: center;
        gap: 12px;

        .range-input {
          flex: 1;
          height: 6px;
          border-radius: 3px;
          background: #d9d9d9;
          outline: none;
          -webkit-appearance: none;

          &::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #1890ff;
            cursor: pointer;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }

          &::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #1890ff;
            cursor: pointer;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;

            &::-webkit-slider-thumb {
              cursor: not-allowed;
            }

            &::-moz-range-thumb {
              cursor: not-allowed;
            }
          }
        }

        .range-value {
          min-width: 40px;
          text-align: center;
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }
      }

      &.readonly {
        .field-input,
        .field-select {
          background-color: #f5f5f5;
          border-color: #e8e8e8;
          cursor: default;
        }

        .field-radio-group,
        .field-checkbox-group {
          .radio-item,
          .checkbox-item {
            cursor: default;

            .radio-input,
            .checkbox-input {
              pointer-events: none;
            }
          }
        }
      }
    }

    .form-section {
      margin-bottom: 32px;

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e8e8e8;
      }

      .section-content {
        .form-grid {
          gap: 16px;
        }
      }
    }
  }

  .form-footer {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    .footer-btn {
      padding: 10px 24px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background-color: #fff;
      color: #333;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.primary {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;

        &:hover {
          background-color: #40a9ff;
        }
      }

      &.danger {
        border-color: #ff4d4f;
        color: #ff4d4f;

        &:hover {
          background-color: #ff4d4f;
          color: #fff;
        }
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          border-color: #d9d9d9;
          color: #333;
          background-color: #fff;
        }
      }

      &.loading {
        position: relative;
        color: transparent;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 16px;
          height: 16px;
          margin: -8px 0 0 -8px;
          border: 2px solid #fff;
          border-top: 2px solid transparent;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .form-loading {
    position: relative;
    min-height: 200px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      z-index: 10;
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 32px;
      height: 32px;
      margin: -16px 0 0 -16px;
      border: 3px solid #f0f0f0;
      border-top: 3px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 11;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 移动端适配
@media (max-width: 768px) {
  .data-form {
    .form-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .form-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .form-body {
      .form-grid {
        grid-template-columns: 1fr;
        gap: 16px;

        &.two-columns {
          grid-template-columns: 1fr;
        }
      }

      .form-field {
        .field-radio-group,
        .field-checkbox-group {
          flex-direction: column;
          gap: 8px;
        }
      }
    }

    .form-footer {
      flex-direction: column;

      .footer-btn {
        width: 100%;
        text-align: center;
      }
    }
  }
}